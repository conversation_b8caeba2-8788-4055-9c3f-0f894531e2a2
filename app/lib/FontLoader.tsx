'use client'

import { useEffect, useState } from 'react'

interface FontChunk {
  name: string
  path: string
  unicodeRange?: string
  characters?: string
}

interface FontConfig {
  fontFamily: string
  basePath: string
  mode: 'chunked' | 'simplified'
  chunks?: FontChunk[]
  fontFile?: string
  fallback: string[]
}

class FontManager {
  private static instance: FontManager
  private loadedChunks = new Set<string>()
  private config: FontConfig | null = null
  private observer: IntersectionObserver | null = null

  static getInstance() {
    if (!FontManager.instance) {
      FontManager.instance = new FontManager()
    }
    return FontManager.instance
  }

  async loadConfig() {
    if (this.config) return this.config
    
    try {
      const response = await fetch('/fonts/split/font-config.json')
      this.config = await response.json()
      return this.config
    } catch (error) {
      console.warn('Failed to load font config:', error)
      return null
    }
  }

  async loadChunk(chunkName: string) {
    if (this.loadedChunks.has(chunkName)) return

    const config = await this.loadConfig()
    if (!config) return

    // 简化模式：直接加载完整字体
    if (config.mode === 'simplified') {
      return this.loadSimplifiedFont(config)
    }

    // 分包模式：加载指定的字体包
    if (!config.chunks) return
    const chunk = config.chunks.find(c => c.name === chunkName)
    if (!chunk) return

    try {
      const fontFace = new FontFace(
        config.fontFamily,
        `url(${config.basePath}/${chunk.path})`,
        {
          unicodeRange: chunk.unicodeRange,
          display: 'swap'
        }
      )

      await fontFace.load()
      document.fonts.add(fontFace)
      this.loadedChunks.add(chunkName)

      console.log(`Font chunk loaded: ${chunkName}`)
    } catch (error) {
      console.warn(`Failed to load font chunk ${chunkName}:`, error)
    }
  }

  async loadSimplifiedFont(config: FontConfig) {
    if (this.loadedChunks.has('simplified')) return

    try {
      const fontFace = new FontFace(
        config.fontFamily,
        `url(${config.basePath}/${config.fontFile})`,
        {
          display: 'swap'
        }
      )

      await fontFace.load()
      document.fonts.add(fontFace)
      this.loadedChunks.add('simplified')

      console.log('Simplified font loaded')
    } catch (error) {
      console.warn('Failed to load simplified font:', error)
    }
  }

  // 预加载基础字符包
  async preloadBasicChunks() {
    const basicChunks = ['basic', 'latin', 'punctuation']
    await Promise.all(basicChunks.map(chunk => this.loadChunk(chunk)))
  }

  // 根据文本内容智能加载字体包
  async loadForText(text: string) {
    const config = await this.loadConfig()
    if (!config) return

    // 简化模式：直接加载完整字体
    if (config.mode === 'simplified') {
      return this.loadSimplifiedFont(config)
    }

    // 分包模式：根据文本内容加载需要的包
    if (!config.chunks) return

    const neededChunks = new Set<string>()

    for (const char of text) {
      for (const chunk of config.chunks) {
        if (chunk.characters && chunk.characters.includes(char)) {
          neededChunks.add(chunk.name)
        }
      }
    }

    const chunksArray = Array.from(neededChunks)
    await Promise.all(chunksArray.map(chunk => this.loadChunk(chunk)))
  }

  // 设置文本观察器，自动加载需要的字体包
  setupTextObserver() {
    if (this.observer) return

    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const text = entry.target.textContent || ''
          this.loadForText(text)
        }
      })
    }, {
      rootMargin: '100px' // 提前100px开始加载
    })

    // 观察所有文本元素
    const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div')
    textElements.forEach(el => this.observer?.observe(el))
  }
}

export function FontLoader() {
  useEffect(() => {
    const fontManager = FontManager.getInstance()

    // 预加载基础字体包
    fontManager.preloadBasicChunks().then(() => {
      // 设置文本观察器
      fontManager.setupTextObserver()
    })

    return () => {
      // 清理观察器
      if (fontManager['observer']) {
        fontManager['observer'].disconnect()
      }
    }
  }, [])

  return null // 这是一个无UI组件
}

// 导出字体管理器实例供其他组件使用
export const fontManager = FontManager.getInstance()

// Hook for manual font loading
export function useFontLoader() {
  const [loading, setLoading] = useState(false)

  const loadForText = async (text: string) => {
    setLoading(true)
    try {
      await fontManager.loadForText(text)
    } finally {
      setLoading(false)
    }
  }

  const preloadChunk = async (chunkName: string) => {
    setLoading(true)
    try {
      await fontManager.loadChunk(chunkName)
    } finally {
      setLoading(false)
    }
  }

  return {
    loading,
    loadForText,
    preloadChunk
  }
}
