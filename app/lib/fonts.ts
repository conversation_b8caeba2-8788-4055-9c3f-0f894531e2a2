import { Inter } from 'next/font/google'

// 使用 CSS 变量来管理 LXGW WenKai 字体（通过 CDN 加载）
export const lxgwWenKai = {
  className: 'font-lxgw-wenkai',
  variable: '--font-lxgw-wenkai',
  style: {
    fontFamily: 'var(--font-lxgw-wenkai), "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif'
  }
}

// Inter 字体作为备选（用于英文内容）
export const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
  fallback: ['system-ui', 'arial'],
  preload: true,
})
