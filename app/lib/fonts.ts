import localFont from 'next/font/local'
import { Inter } from 'next/font/google'

// LXGW WenKai 本地字体配置 - 使用 woff2 格式获得更好的性能
export const lxgwWenKai = localFont({
  src: [
    {
      path: '../fonts/LXGWWenKai-Regular.woff2',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../fonts/LXGWWenKai-Regular.ttf',
      weight: '400',
      style: 'normal',
    }
  ],
  display: 'swap',
  variable: '--font-lxgw-wenkai',
  fallback: [
    'PingFang SC',
    'Hiragino Sans GB',
    'Microsoft YaHei',
    'WenQuanYi Micro Hei',
    'sans-serif'
  ],
  preload: true,
})

// Inter 字体作为备选（用于英文内容）
export const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
  fallback: ['system-ui', 'arial'],
  preload: true,
})
