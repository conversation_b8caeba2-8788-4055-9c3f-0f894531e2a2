@import 'tailwindcss';

@plugin '@tailwindcss/typography';
@plugin "@iconify/tailwind4";
@theme {
  --font-sans: var(--font-lxgw-wenkai), var(--font-inter), 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'WenQuanYi Micro Hei', ui-sans-serif, system-ui, -apple-system,
    BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif,
    Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;

  --animate-fade-in: fade-in 0.2s ease-out;

  @keyframes fade-in {
    0% {
      opacity: 0;
      transform: scale(0.95);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }
}

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

/* 主题色 */
.text-mz {
  --tw-text-opacity: 1;
  color: rgb(129 86 133 / var(--tw-text-opacity, 1));
}

.shiki,
.shiki span {
  @apply !bg-neutral-50;
}

body {
  @apply w-full min-h-screen text-black bg-white;
}

.prose .anchor {
  @apply absolute invisible no-underline;
  margin-left: -1em;
  padding-right: 0.5em;
  width: 80%;
  max-width: 700px;
  cursor: pointer;
}

.anchor:hover {
  @apply visible;
}

.prose a {
  @apply font-medium underline transition-all underline-offset-4 decoration-[0.5px]
  hover:decoration-[0.8px];
}

.prose .anchor:after {
  @apply text-neutral-300;
  content: '#';
}

.prose *:hover > .anchor {
  @apply visible;
}

.prose pre code .remove {
  @apply inline-block  bg-neutral-400/30;
}
.prose code::after {
  content: '';
}
.prose code::before {
  content: '';
}
.prose img {
  @apply rounded-lg;
}
pre {
  overflow: auto;
}

/* Remove Safari input shadow on mobile */
input[type='text'],
input[type='email'] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

table {
  display: block;
  max-width: fit-content;
  overflow-x: auto;
  white-space: nowrap;
}

.title {
  text-wrap: balance;
}

.noto--clapping-hands {
  display: inline-block;
  width: 60px;
  height: 60px;
  vertical-align: middle;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-top: -20px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 128 128'%3E%3Cpath fill='%23bdccd4' d='M19.7 85.2c-1.2.1-10.6.5-12 .6c-2.6.2-2.4 4.6.2 4.5c1.4-.1 12-.6 12-.6c2.8-.3 2.5-4.7-.2-4.5m9.3 15c-.4 1.2-3.4 10-3.9 11.4c-.8 2.5 3.4 3.9 4.2 1.4c.5-1.4 3.9-11.4 3.9-11.4c.8-2.5-3.4-3.9-4.2-1.4m82.4-81.8c-.9.8-8.2 6.7-9.2 7.7c-2 1.7.8 5.1 2.8 3.4c1.1-.9 9.2-7.7 9.2-7.7c2.1-1.8-.8-5.2-2.8-3.4M81 12.2c.4 1.2 2.9 10.2 3.3 11.5c.7 2.5 5 1.3 4.3-1.2c-.4-1.4-3.3-11.5-3.3-11.5c-.8-2.6-5.1-1.4-4.3 1.2M23.1 93.1c-1 .7-14 9.8-15.2 10.7c-2.1 1.5.5 5.2 2.6 3.6c1.2-.9 15.2-10.7 15.2-10.7c2.2-1.7-.5-5.3-2.6-3.6M99.6 5.8C99.2 7 93.8 22 93.4 23.4c-.8 2.5 3.4 4 4.2 1.5c.5-1.4 6.2-17.6 6.2-17.6c.8-2.6-3.4-4-4.2-1.5'/%3E%3Cpath fill='%23ffca28' d='M99 44.7c1.9-4.5 5.5-7.7 9.7-6.7c2.9.6 3.5 3.3 3.3 4.9c-2.3 15.9 1.1 28.1.2 39.9c-1.1 14.9-10.5 30.3-30.2 31.7c-12.5 3-26.4.9-37.6-10c-6.2-6-11.6-10.6-19.1-22.8c-3.1-5.1-7.9-13.2-7.9-18.5c0-4.5 4.5-5.9 6.8-3.8c-3-4.8-4.4-8.3-4.5-12.4c-.1-3.4 4.1-5.8 6.8-3.2c-2.3-3.7-5.5-10.2-.7-13.5c1.5-1 5.3-2.3 9.9 3.5c-1.2-2.3-2.5-6.2.7-8.8c1.7-1.4 5-1.8 7.8 1.4c-.2-1.2 1.5-4.2 4.3-4.3c3.4-.2 6.1 2.9 8.1 5.2c-2-8 6.3-10.3 10.9-4.9c2.4 2.7 8 9.5 16.4 23.5c-.2-8.4 3.9-15.8 9.9-14.7c2.1.4 4.2 1.8 4.4 5.7c.4 3.5.8 7.8.8 7.8'/%3E%3Cdefs%3E%3Cpath id='notoClappingHands0' d='M99 44.7c1.9-4.5 5.5-7.7 9.7-6.7c2.9.6 3.5 3.3 3.3 4.9c-2.3 15.9 1.1 28.1.2 39.9c-1.1 14.9-10.5 30.3-30.2 31.7c-12.5 3-26.4.9-37.6-10c-6.2-6-11.6-10.6-19.1-22.8c-3.1-5.1-7.9-13.2-7.9-18.5c0-4.5 4.5-5.9 6.8-3.8c-3-4.8-4.4-8.3-4.5-12.4c-.1-3.4 4.1-5.8 6.8-3.2c-2.3-3.7-5.5-10.2-.7-13.5c1.5-1 5.3-2.3 9.9 3.5c-1.2-2.3-2.5-6.2.7-8.8c1.7-1.4 5-1.8 7.8 1.4c-.2-1.2 1.5-4.2 4.3-4.3c3.4-.2 6.1 2.9 8.1 5.2c-2-8 6.3-10.3 10.9-4.9c2.4 2.7 8 9.5 16.4 23.5c-.2-8.4 3.9-15.8 9.9-14.7c2.1.4 4.2 1.8 4.4 5.7c.4 3.5.8 7.8.8 7.8'/%3E%3C/defs%3E%3CclipPath id='notoClappingHands1'%3E%3Cuse href='%23notoClappingHands0'/%3E%3C/clipPath%3E%3Cpath fill='%23faa700' d='M75.7 114c-7.6-.2-20.6.3-37-17.9c-2.9-3.2-13.7-16.3-19.4-31.3c-.5-1.2-.4-3.9 1.6-4.6s3.4 1.1 4.5 2.8c.8 1.2 5.1 8.3 7.6 11.2c1 1.1 1.5.3 1.7.1c-5.7-9.7-13.3-22.1-13.3-26.8c0-2.6 2.8-4.3 4.8-1.7c1.6 2 8.7 10.9 9.6 12.1c.9 1.1 1.6.2 1.9 0c-4.1-5.3-13.1-19.1-12.4-23.5c.4-2.6 3.9-4.5 6.3-2.1c1.8 1.8 11.7 12.8 11.7 12.8s1 1.1 1.8.1c-4.8-6-11.5-15.3-7.5-18.6c1.6-1.3 3.7-1.9 8 2.7c2.8 3 21.6 20 38.2 31c1.6 1 2.7-.6 2.6-2.1c-1.4-14.9.1-25.6 6.4-25.7c2.9 0 4.1 2.2 4.3 5.2c.2 2.9 1.1 11 3.2 19.3c2.1 8.4 5.2 20.1 4.3 28.9s-7.2 19.4-13.1 23c-6 3.6-10.1 4.5-10.8 4.7s-1.7 2.4.8 1.9c2.5-.4 10.2-1.1 10.2-1.1l11.1-5.9l9.5-11.9l4.1-16.9l-1.3-48.4L88 26.6l-25.6-12l-42.1 11l-7.2 40.2l17.3 30l21.5 19.9s22.4 1.6 24 1.4c1.5-.1 2.3-3-.2-3.1' clip-path='url(%23notoClappingHands1)'/%3E%3Cdefs%3E%3Cuse href='%23notoClappingHands0' id='notoClappingHands2'/%3E%3C/defs%3E%3CclipPath id='notoClappingHands3'%3E%3Cuse href='%23notoClappingHands2'/%3E%3C/clipPath%3E%3Cg fill='%23b55e19' clip-path='url(%23notoClappingHands3)'%3E%3Cpath d='M96 30.2c-.7.4-.7 1.1 0 2.2c.5.8.9 1.8.8 4.4c-.4 20 6.8 28.3 6.4 47.2c-.4 20.3-15.8 32.5-40.4 29.6c-2.5-.3-1.7 2-1.7 2s6.5 1.4 8.9 1.2c8.5-.5 12.1-2.5 12.1-2.5c12.3-2.6 24.2-14.5 24.2-30.5c0-12.9-5.7-28.1-7.2-39.2c-.5-3.7 1.1-7.4 1.4-9.3c.3-3.1-2-6.5-4.5-5.1M24.3 59.3c-.6-1-1.5-.4-1 .6c.6 1.2 5.7 12.5 16.6 24.7c.5.5 1 1.5 1.1 1.9c.3 1.4 2 1.2 2.3 0c.1-.5.3-1 .4-1.5c.1-.3.2-1-.4-1.6c-6.5-6.5-12.8-13.9-19-24.1m2.2-15.6c-.3-.4-.7-.8-.8-1.1c-.5.2-.8.5-.3 1.7c4.3 9.5 17.8 23.5 21.1 26.7c.8.8.9 1.3 1.1 2c.7 2.6 2.3 2.3 2.9 1.2c.4-.8.8-1.5.9-1.9s.2-.9-.4-1.4c-8.3-7.3-17.8-18.3-24.5-27.2m9.3-10.1c-.5-.8-1.4-2.1-1.4-2.1s-1.2.3-.4 1.8c4.8 9 17.4 23.2 22.3 27.2c.5.4 1.3 1.5 1.6 2.4c.6 1.9 2.1 1.8 2.9.8c.4-.5.8-1 1-1.6c.3-.5.1-1.2-.7-1.9c-14.3-12.3-25.3-26.6-25.3-26.6m22.7-14.2c-.5 1.5-1.6 4 .4 8.1c3.7 7.7 11.3 14.8 16.1 26.7c-1.4-1-2.5-1.9-4.2-3.2c-3.5-8.4-13.3-19.5-14.1-24c-.6-3.1-1.4-6.3-1.4-6.3z'/%3E%3C/g%3E%3C/svg%3E");
}

/* 全局 Loading 样式 */
.loader-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 9999;
}

/* HTML: <div class="loader"></div> */
.loader {
  font-weight: bold;
  font-family: monospace;
  font-size: 30px;
  display: inline-grid;
}
.loader:before,
.loader:after {
  content: 'Loading...';
  grid-area: 1/1;
  -webkit-mask: linear-gradient(90deg, #000 50%, #0000 0) 0 50%/2ch 100%;
  mask: linear-gradient(90deg, #000 50%, #0000 0) 0 50%/2ch 100%;
  animation: l11 1s infinite cubic-bezier(0.5, 220, 0.5, -220);
}
.loader:after {
  -webkit-mask-position: 1ch 50%;
  mask-position: 1ch 50%;
  --s: -1;
}
@keyframes l11 {
  100% {
    transform: translateY(calc(var(--s, 1) * 0.1%));
  }
}

@media (width >= 52rem) {
  html body {
    position: relative;
    z-index: 0;

    &::before {
      content: '';
      background-image: url(/2291742460149_.pic.png);
      background-size: contain;
      background-repeat: no-repeat;
      background-position: right;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0.6;
      z-index: -1;
    }
  }

  html.charm.dark body {
    &::before {
      filter: brightness(0.8);
    }
  }
}
