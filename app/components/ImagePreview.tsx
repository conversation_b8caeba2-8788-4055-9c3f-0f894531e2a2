'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'

interface ImagePreviewProps {
  src: string
  alt: string
  width: number
  height: number
}

export function ImagePreview({ src, alt, width, height }: ImagePreviewProps) {
  const [isPreview, setIsPreview] = useState(false)

  // 按ESC键关闭预览
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setIsPreview(false)
      }
    }
    window.addEventListener('keydown', handleEsc)
    return () => window.removeEventListener('keydown', handleEsc)
  }, [])

  return (
    <>
      <div className="relative">
        <Image
          src={src}
          alt={alt}
          width={width}
          height={height}
          className="rounded-md shadow-md dark:shadow-neutral-800 hover:cursor-zoom-in"
          onClick={() => setIsPreview(true)}
          style={{
            width: 'max-content',
            height: 'auto',
          }}
          quality={80}
        />
      </div>

      {isPreview && (
        <div
          className="fixed inset-0 bg-black/60 backdrop-blur-xs z-50 flex items-center justify-center cursor-zoom-out"
          onClick={() => setIsPreview(false)}
        >
          <div className="animate-fade-in relative max-w-[90vw] max-h-[90vh]">
            <Image
              src={src}
              alt={alt}
              width={width * 1.5}
              height={height * 1.5}
              className="rounded-lg shadow-2xl cursor-zoom-out"
              quality={80}
              style={{
                width: 'auto',
                height: 'auto',
                maxWidth: '90vw',
                maxHeight: '90vh'
              }}
              onClick={() => setIsPreview(false)}
            />
          </div>
        </div>
      )}
    </>
  )
} 
