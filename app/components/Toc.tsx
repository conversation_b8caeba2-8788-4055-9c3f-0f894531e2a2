'use client'

import { useState, useEffect } from 'react'

interface TocItem {
  id: string
  text: string
  level: number
}

interface TocProps {
  isInline?: boolean
}

function slugify(text: string): string {
  return text
    .toLowerCase()
    .replace(/[\s#]/g, '-') // 替换空格和#为连字符
    .replace(/[^\w\u4e00-\u9fff-]/g, '') // 只保留字母、数字、中文和连字符
    .replace(/-+/g, '-') // 将多个连字符替换为单个
    .replace(/^-+|-+$/g, '') // 删除首尾的连字符
}

export function Toc({ isInline = false }: TocProps) {
  const [toc, setToc] = useState<TocItem[]>([])
  const [isOpen, setIsOpen] = useState(true)

  useEffect(() => {
    // 获取所有 h2 和 h3 标题
    const headings = document.querySelectorAll('h2, h3')
    const tocItems = Array.from(headings)
      .map((heading, index) => {
        const text = heading.textContent?.replace(/§|#/g, '').trim() || ''
        // 如果标题没有 id，生成一个
        const id = heading.id || slugify(text) || `heading-${index}`
        // 设置标题的 id
        if (!heading.id) {
          heading.id = id
        }
        return {
          id,
          text,
          level: Number(heading.tagName[1])
        }
      })
      .filter((item) => item.text) // 过滤掉空标题

    setToc(tocItems)
  }, [])

  if (toc.length === 0) return null

  // 侧边目录样式
  if (!isInline) {
    return (
      <nav className="hidden lg:block fixed left-2 top-20 w-64 max-h-[calc(100vh-6rem)] overflow-auto">
        <div className="p-4 rounded-lg shadow-xs">
          <ul className="space-y-3">
            {toc.map(({ id, text, level }) => (
              <li key={id} className=" underline underline-offset-4 decoration-[0.5px] ">
                <a
                  href={`#${id}`}
                  className={`
                    block text-base transition-colors
                    ${level === 2 ? 'pl-2' : 'pl-6 text-[13px] opacity-80'}
                    text-neutral-600 dark:text-neutral-300 
                    hover:text-orange-500
                  `}
                  onClick={(e) => {
                    e.preventDefault()
                    document.getElementById(id)?.scrollIntoView({
                      behavior: 'smooth',
                      block: 'start'
                    })
                  }}
                >
                  {text}
                </a>
              </li>
            ))}
          </ul>
        </div>
      </nav>
    )
  }

  // 内嵌折叠目录样式
  return (
    <div className="my-2 bg-neutral-100 rounded-md p-2">
      <div onClick={() => setIsOpen(!isOpen)} className="cursor-pointer">
        {!isOpen ? (
          <span className="icon-[solar--alt-arrow-right-bold] text-2xl"></span>
        ) : (
          <span className="icon-[solar--alt-arrow-down-bold] text-2xl "></span>
        )}
      </div>

      {isOpen && (
        <ul className="mt-3 space-y-2 pt-2">
          {toc.map(({ id, text, level }) => (
            <li key={id} className="text-sm hover:underline">
              <a
                href={`#${id}`}
                className={`
                  block transition-colors
                  ${level === 2 ? 'font-medium' : 'pl-4 text-[13px] opacity-90'}
                  text-neutral-700 dark:text-neutral-300 
                  hover:text-orange-500
                `}
                onClick={(e) => {
                  e.preventDefault()
                  document.getElementById(id)?.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                  })
                  // 在移动设备上，点击后可以折叠目录
                  window.innerWidth < 768 && setIsOpen(false)
                }}
              >
                {text}
              </a>
            </li>
          ))}
        </ul>
      )}
    </div>
  )
}
