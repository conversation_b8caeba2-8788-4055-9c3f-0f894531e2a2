import { Send } from 'lucide-react'

export default function Footer() {
  return (
    <footer className="mb-16">
      <ul className="font-sm mt-8 flex flex-col space-x-0 space-y-2 text-neutral-600 md:flex-row md:space-x-4 md:space-y-0 dark:text-neutral-300">
        <li>
          <a
            className="flex items-center transition-all hover:text-neutral-800 dark:hover:text-neutral-100"
            rel="noopener noreferrer"
            target="_blank"
            title="@我"
            href="mailto:<EMAIL>"
          >
            <Send strokeWidth={1} className="w-4 h-4 mr-1 text-orange-400" />
            邮箱
          </a>
        </li>
      </ul>
      <div className="mt-8 flex  items-center gap-4 text-neutral-600 dark:text-neutral-300">
        <p>©{new Date().getFullYear()} Reimux</p>
        {/* <p>No DarkMode</p> */}
      </div>
    </footer>
  )
}
