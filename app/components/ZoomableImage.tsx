"use client";

import React, { useEffect, useState } from 'react';
import Image from "next/legacy/image";
import Zoom from 'react-medium-image-zoom';
import 'react-medium-image-zoom/dist/styles.css';

const ZoomableImage = ({ src, alt, ...props }) => {
  const [originalDimensions, setOriginalDimensions] = useState({ width: 700, height: 475 });

  useEffect(() => {
    const img = new window.Image();
    img.src = src;
    img.onload = () => {
      setOriginalDimensions({ width: img.width, height: img.height });
    };
  }, [src]);

  return (
    <div className="w-full max-w-full relative my-1.5">
      <Zoom zoomImg={{ src, alt }}>
        <Image
          src={src}
          alt={alt}
          layout="responsive"
          width={originalDimensions.width}
          height={originalDimensions.height}
          className="w-full h-auto cursor-pointer"
          {...props}
        />
      </Zoom>
    </div>
  );
};

export default ZoomableImage;