import Link from 'next/link'
import { MDXRemote } from 'next-mdx-remote/rsc'
import React from 'react'
import { codeToHtml } from 'shiki'
import { transformerNotationDiff } from '@shikijs/transformers'
import Image from 'next/image'
function Table({ data }) {
  let headers = data.headers.map((header, index) => <th key={index}>{header}</th>)
  let rows = data.rows.map((row, index) => (
    <tr key={index}>
      {row.map((cell, cellIndex) => (
        <td key={cellIndex}>{cell}</td>
      ))}
    </tr>
  ))

  return (
    <table>
      <thead>
        <tr>{headers}</tr>
      </thead>
      <tbody>{rows}</tbody>
    </table>
  )
}

function CustomLink(props: any) {
  let href = props.href

  if (href.startsWith('/')) {
    return (
      <Link href={href} {...props}>
        {props.children}
      </Link>
    )
  }

  if (href.startsWith('#')) {
    return <a {...props} />
  }

  return <a target="_blank" rel="noopener noreferrer" {...props} />
}
async function Code({ children, ...props }) {
  const className = props.className || ''
  const [lang, filePath] = className.replace('language-', '').split(':')

  let codeHTML = await codeToHtml(children, {
    lang,
    themes: {
      dark: 'vitesse-dark',
      light: 'vitesse-light'
    },
    transformers: [transformerNotationDiff()]
  })

  return (
    <div className="relative group">
      <div className="flex justify-between items-center opacity-0 group-hover:opacity-100 transition-opacity duration-150 absolute top-1 right-2 text-neutral-400 text-xs">
        {filePath ? <span className="mr-4">{filePath}</span> : null}
        <span>{lang}</span>
      </div>
      <div {...props} dangerouslySetInnerHTML={{ __html: codeHTML }}></div>
    </div>
  )
}

function slugify(str: any) {
  return str
    .toString()
    .toLowerCase()
    .replace(/[\s#]/g, '-') // Replace spaces and # with hyphens
    .replace(/[^\w\u4e00-\u9fff-]/g, '') // Only keep letters, numbers, Chinese characters and hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with a single one
    .replace(/^-+|-+$/g, '') // Remove hyphens from the beginning and end
}

function createHeading(level: any) {
  const Heading = ({ children }) => {
    // Convert children to string if it's not already
    const childText = typeof children === 'string' ? children : children?.toString() || ''
    let slug = slugify(childText)

    return React.createElement(
      `h${level}`,
      { id: slug },
      [
        React.createElement('a', {
          href: `#${slug}`,
          key: `link-${slug}`,
          className: 'anchor'
        })
      ],
      children
    )
  }

  Heading.displayName = `Heading${level}`

  return Heading
}

let components = {
  p: (props: any) => {
    if (props.children.type === 'img') {
      return (
        <Image
          src={props.children.props.src}
          alt={props.children.props.alt || ''}
          width={1200}
          height={800}
        />
      )
    } else {
      return <p {...props}></p>
    }
  },
  h1: createHeading(1),
  h2: createHeading(2),
  h3: createHeading(3),
  h4: createHeading(4),
  h5: createHeading(5),
  h6: createHeading(6),
  a: CustomLink,
  pre: ({ children }) => <Code {...children.props} />,
  code: ({ children }) => (
    <>
      <code>
        <span {...children.props}>{children}</span>
      </code>
    </>
  ),
  Table
}

export function CustomMDX(props: any) {
  return <MDXRemote {...props} components={{ ...components, ...(props.components || {}) }} />
}
