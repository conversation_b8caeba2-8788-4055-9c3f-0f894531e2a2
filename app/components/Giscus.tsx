"use client";
import { useEffect, useState } from 'react';
import { useTheme } from 'next-themes';

const Giscus = () => {
  const { theme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    const giscusTheme = theme === 'dark' || resolvedTheme === 'dark' ? 'dark' : 'light';
    // console.log(`Giscus theme: ${giscusTheme}`);
    
    const script = document.createElement('script');
    script.src = 'https://giscus.app/client.js';
    script.setAttribute('data-repo', 'whcxxb/blog-discussion');
    script.setAttribute('data-repo-id', 'R_kgDOKGkOSA');
    script.setAttribute('data-category', 'Announcements');
    script.setAttribute('data-category-id', 'DIC_kwDOKGkOSM4CYku2');
    script.setAttribute('data-mapping', 'pathname');
    script.setAttribute('data-reactions-enabled', '1');
    script.setAttribute('data-emit-metadata', '0');
    script.setAttribute('data-input-position', 'top');
    script.setAttribute('data-theme', giscusTheme);
    script.setAttribute('data-lang', 'zh-CN');
    script.setAttribute('data-loading', 'lazy');
    script.crossOrigin = 'anonymous';
    script.async = true;

    const container = document.getElementById('giscus-container');
    // 清除旧的评论组件
    if (container) {
      container.innerHTML = '';
      container.appendChild(script);
    }

    // 监听主题变化
    const iframe = document.querySelector<HTMLIFrameElement>('iframe.giscus-frame');
    if (iframe) {
      iframe.contentWindow?.postMessage(
        { giscus: { setConfig: { theme: giscusTheme } } },
        'https://giscus.app'
      );
    }
  }, [theme, resolvedTheme, mounted]);

  return <div id="giscus-container" className="mt-10" />;
};

export default Giscus;
