'use client';

import Link from 'next/link';
import { useState } from 'react';
import { usePathname } from 'next/navigation';

export function Navbar() {
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const pathname = usePathname();

  const menuItems = [
    {
      path: '/',
      text: 'Home',
      icons: {
        default: "icon-[solar--file-text-broken]",
        hover: "icon-[solar--file-smile-outline]",
        active: "icon-[solar--file-smile-bold-duotone]"
      }
    },
    {
      path: '/about',
      text: 'About',
      icons: {
        default: "icon-[solar--ghost-broken]",
        hover: "icon-[solar--ghost-smile-outline]",
        active: "icon-[solar--ghost-smile-bold-duotone]"
      }
    }
  ];

  return (
    <aside className="-ml-[8px] mb-16 tracking-tight">
      <div className="lg:sticky lg:top-20">
        <nav className="flex items-center gap-4">
          {menuItems.map((item) => (
            <Link 
              key={item.path}
              href={item.path}
              className="relative group"
              onMouseEnter={() => setHoveredItem(item.path)}
              onMouseLeave={() => setHoveredItem(null)}
            >
              <div className="relative flex items-center">
                {/* 默认图标 */}
                <span 
                  className={`${item.icons.default} text-3xl transition-opacity duration-200
                    ${hoveredItem === item.path || pathname === item.path ? 'opacity-0' : 'opacity-100'}`}
                />
                {/* hover图标 */}
                <span 
                  className={`${item.icons.hover} text-3xl absolute left-0 transition-opacity duration-200
                    ${hoveredItem === item.path && pathname !== item.path ? 'opacity-100' : 'opacity-0'}`}
                />
                {/* active图标 */}
                <span 
                  className={`${item.icons.active} text-3xl absolute left-0 transition-opacity duration-200
                    ${pathname === item.path ? 'opacity-100' : 'opacity-0'}`}
                />
              </div>
              {/* 文本 */}
              <span 
                className={`absolute left-1/2 -translate-x-1/2 whitespace-nowrap
                  transition-all duration-300 ease-in-out
                  text-orange-500
                  ${hoveredItem === item.path ? 'opacity-100 translate-y-3' : 'opacity-0 translate-y-2'}
                `}
              >
                {item.text}
              </span>
            </Link>
          ))}
        </nav>
      </div>
    </aside>
  );
}
