'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'

const navItems = {
  '/': {
    name: 'Home',
    icon: 'solar--file-smile-bold-duotone'
  },
  '/blog': {
    name: 'Blog'
  }
}

export function Navbar() {
  const pathname = usePathname()

  return (
    <aside className="-ml-[8px] mb-16 tracking-tight">
      <div className="lg:sticky lg:top-20">
        <nav className="flex items-center relative px-0 pb-0 fade md:overflow-auto scroll-pr-6 md:relative">
          <div className="flex flex-row space-x-0 pr-10">
            {Object.entries(navItems).map(([path, { name }]) => {
              const isActive = pathname === path || (path !== '/' && pathname?.startsWith(path))

              return (
                <Link
                  key={path}
                  href={path}
                  className={`
                    py-1 px-2 m-1
                    ${
                      isActive
                        ? 'text-neutral-800 dark:text-neutral-200 font-medium'
                        : 'text-neutral-500 hover:text-neutral-800 dark:hover:text-neutral-200'
                    }
                  `}
                >
                  {name}
                </Link>
              )
            })}
            {/* <button
              onClick={() => query.toggle()}
              className="py-1 px-2 m-1 text-neutral-500 hover:text-neutral-800 dark:hover:text-neutral-200"
            >
              Search
              <kbd className="ml-2 px-2 py-1 text-xs bg-neutral-200 dark:bg-neutral-700 rounded">⌘+K</kbd>
            </button> */}
          </div>
        </nav>
      </div>
    </aside>
  )
}
