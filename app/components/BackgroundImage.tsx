'use client'

import Image from 'next/image'
import { useTheme } from 'next-themes'
import { useEffect, useState } from 'react'

export default function BackgroundImage() {
  const { theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  // 避免水合不匹配
  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  return (
    <div className="fixed inset-0 z-[-1] hidden lg:block">
      <Image
        src="/2291742460149_.pic.png"
        alt="Background decoration"
        fill
        priority
        className={`object-contain object-right opacity-60 ${
          theme === 'dark' ? 'brightness-75' : ''
        }`}
        sizes="100vw"
      />
    </div>
  )
}
