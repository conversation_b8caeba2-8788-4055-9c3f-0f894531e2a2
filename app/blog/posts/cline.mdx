---
title: 'Cline + deepseek R1 体验'
summary: 'Cline + deepseek R1 体验'
publishedAt: '2025-03-18'
published: false
---

最近cursor白嫖的日子越来越难了，准备试试 Cline 搭配 deepseek 官方的API

### 搜索 cline 安装插件

![](https://gcore.jsdelivr.net/gh/whcxxb/blog-imgs@main/imgs/202503181439812.png)

### 配置 deepseek

![](https://gcore.jsdelivr.net/gh/whcxxb/blog-imgs@main/imgs/202503181441630.png)

### 计划模式（Plan Mode）

	+ 在此模式下，Cline 扮演架构师的角色，专注于收集信息、提出澄清性问题，并设计解决方案。 ￼
	+ 为用户提供清晰、详细的任务规划，确保在执行阶段能够高效地完成任务。 ￼
  + 收集相关信息，获取任务的上下文。
  +	与用户沟通，提出必要的澄清性问题。 ￼
  +	设计详细的任务执行计划，可能包括流程图或其他可视化工具。 ￼
  +	与用户讨论并获取反馈，确保计划的可行性和完整性。 ￼

  可以在plan 模式下使用 deepseek R1 进行问题方案生成

### 执行模式（Act Mode）

	+	在此模式下，Cline 更像是一个执行者，利用之前阶段制定的计划和收集的信息，专注于实际任务的执行。 ￼
	+	高效地完成用户的任务请求，确保结果符合预期。
  +	使用各种工具和资源来执行任务。
  + 在执行过程中，根据需要进行调整和优化。
  +	完成任务后，向用户展示结果，并提供必要的解释或后续步骤建议。

  使用 plan生成的方案 在 Act模式下使用 deepseek chat 生成代码
