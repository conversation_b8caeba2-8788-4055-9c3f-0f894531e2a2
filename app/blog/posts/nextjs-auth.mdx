---
title: 'NextJs+Supabase+Tailwind+shadcn/ui快速实现邮箱登录验证'
summary: '使用 Next.js 14 和 Supabase 快速搭建一个带邮箱验证的登录系统，UI 使用 Tailwind 和 shadcn/ui 实现美观的界面'
publishedAt: '2025-01-17'
published: false
---

这篇文章将介绍如何使用最新的技术栈快速实现一个邮箱登录系统。我们将使用：
- Next.js 14 作为全栈框架
- Supabase 作为后端服务（免费额度足够测试和小型项目使用）
- Tailwind CSS 处理样式
- shadcn/ui 提供漂亮的 UI 组件

## 1. 创建项目

首先创建一个新的 Next.js 项目，我们使用官方推荐的方式：

```bash
pnpm create next-app@latest my-auth-app
```

在创建过程中，选择以下配置：
- TypeScript: Yes ✅
- Tailwind CSS: Yes ✅
- App Router: Yes ✅
- 其他选项按需选择

## 2. 安装依赖

进入项目目录，安装必要的依赖：

```bash
cd my-auth-app

# 安装 Supabase 客户端
pnpm add @supabase/supabase-js @supabase/auth-helpers-nextjs

# 安装 shadcn/ui 及其依赖
pnpm add @radix-ui/react-slot class-variance-authority clsx tailwind-merge
pnpm dlx shadcn-ui@latest init
```

在 shadcn/ui 初始化时的选择：
- 样式：Default
- 基础颜色：Slate
- 全局 CSS 位置：app/globals.css
- 组件目录：components/ui
- 工具类目录：lib/utils
- React Server Components: Yes
- 组件样式：Default Style

## 3. 配置 Supabase

1. 前往 [Supabase](https://supabase.com) 创建账号并新建项目
2. 在项目设置中找到 API 密钥，创建 `.env.local` 文件：

```text
NEXT_PUBLIC_SUPABASE_URL=你的项目URL
NEXT_PUBLIC_SUPABASE_ANON_KEY=你的公共密钥
```

3. 在 Supabase 控制台中配置邮箱模板：
   - Authentication → Email Templates
   - 可以自定义邮件内容和样式

## 4. 创建登录组件

创建一个登录表单组件：

```tsx:app/components/auth/login-form.tsx
'use client'

import { useState } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { toast } from '@/components/ui/use-toast'

export function LoginForm() {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const supabase = createClientComponentClient()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    
    try {
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: `${location.origin}/auth/callback`
        }
      })

      if (error) throw error
      
      toast({
        title: '验证邮件已发送',
        description: '请检查你的邮箱并点击验证链接'
      })
    } catch (error) {
      toast({
        title: '发送失败',
        description: '请检查邮箱地址是否正确',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleLogin} className="space-y-4">
      <Input
        type="email"
        placeholder="输入你的邮箱"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        required
      />
      <Button type="submit" className="w-full" disabled={loading}>
        {loading ? '发送中...' : '登录/注册'}
      </Button>
    </form>
  )
}
```

## 5. 创建登录页面

```tsx:app/auth/login/page.tsx
import { LoginForm } from '@/components/auth/login-form'

export default function LoginPage() {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="max-w-sm w-full p-6 bg-white rounded-lg shadow-lg">
        <h1 className="text-2xl font-bold text-center mb-6">
          欢迎使用
        </h1>
        <LoginForm />
      </div>
    </div>
  )
}
```

## 6. 处理验证回调

创建回调处理页面：

```tsx:app/auth/callback/route.ts
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export async function GET(request: Request) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')

  if (code) {
    const supabase = createRouteHandlerClient({ cookies })
    await supabase.auth.exchangeCodeForSession(code)
  }

  return NextResponse.redirect(requestUrl.origin)
}
```

## 7. 添加中间件

创建中间件来处理认证状态：

```ts:middleware.ts
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient({ req: request, res })

  const {
    data: { session }
  } = await supabase.auth.getSession()

  // 如果用户未登录且访问受保护的路由，重定向到登录页
  if (!session && !request.nextUrl.pathname.startsWith('/auth')) {
    return NextResponse.redirect(new URL('/auth/login', request.url))
  }

  return res
}

export const config = {
  matcher: ['/((?!_next/static|_next/image|favicon.ico).*)']
}
```

## 实际效果

登录流程是这样的：
1. 用户输入邮箱点击登录
2. 收到一封带验证链接的邮件
3. 点击链接完成验证
4. 自动跳转到首页

优点：
- 不需要记住密码
- 更安全（每次登录都需要邮箱验证）
- 实现简单，维护成本低

[这里放实际运行的截图和效果展示]

## 进阶优化

1. 添加加载状态动画
2. 自定义邮件模板样式
3. 添加记住登录状态功能
4. 集成社交账号登录

完整代码可以在 [GitHub 仓库](你的仓库地址) 查看。

如果遇到问题，欢迎在评论区讨论！



