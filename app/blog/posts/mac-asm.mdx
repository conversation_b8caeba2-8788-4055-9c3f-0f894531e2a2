---
title: MAC 优雅使用 ASM 入网小助手
summary: MAC 优雅使用 ASM 入网小助手
publishedAt: 2025-04-27
published: true
---

公司网络需要安装一个叫 `ASM入网助手` 的 app 来认证才能访问网络,我之前干活都是用自己的手机热点+公司VPN,但是有的项目必须使用公司的网络才能访问🤡,这东西和 EasyConnect一桌的，会在你系统启动一堆垃圾进程,而且还不好关闭。


## 禁用网络扩展

首先安装 ASM 后 在 设置-通用-登录项与扩展中-网络扩展 禁用后台监控

![](https://gcore.jsdelivr.net/gh/whcxxb/blog-imgs@main/imgs/20250427161519.png)

## 关闭和启动

启动 ASM 后如果要切换到自己网络使用一定要退出这个程序，否则会疯狂占用系统资源 🤮

![](https://gcore.jsdelivr.net/gh/whcxxb/blog-imgs@main/imgs/20250427161845.png)

还不能手动退出，它会自我启动，所以只能按照一下步骤来退出

```bash
# 1. 移除可执行的权限
chmod -x /Applications/ASM.app/Contents/MacOS/ASM 

# 2. 列出所有ASM 的进程 和 PID
ps aux | grep ASM   

# 3. 结束进程
kill -9 <PID>
```
写一个脚本来完成自动化操作

```bash
# 赋予执行权限
chmod +x block_asm.sh

# 关闭 ASM
./block_asm.sh

# 恢复 ASM 的可执行权限
./block_asm.sh restore

```

保存为 `block_asm.sh`

```bash
# 定义变量
APP_PATH="/Applications/ASM.app/Contents/MacOS/ASM"
APP_NAME="ASM"
SERVICE_HELPER="com.infogo.ServiceHelper"

# 检查是否以恢复模式运行
if [ "$1" == "restore" ]; then
    echo "正在恢复 $APP_PATH 的可执行权限..."
    if [ -f "$APP_PATH" ]; then
        sudo chmod +x "$APP_PATH"
        if [ $? -eq 0 ]; then
            echo "权限恢复成功！"
        else
            echo "错误：无法恢复权限，请检查文件路径或权限。"
            exit 1
        fi
    else
        echo "错误：$APP_PATH 不存在，请检查路径。"
        exit 1
    fi
    exit 0
fi

# 步骤 1：移除 ASM 可执行权限
echo "正在移除 $APP_PATH 的可执行权限..."
if [ -f "$APP_PATH" ]; then
    sudo chmod -x "$APP_PATH"
    if [ $? -eq 0 ]; then
        echo "可执行权限已移除，ASM 无法启动。"
    else
        echo "错误：无法移除权限，请检查文件路径或权限。"
        exit 1
    fi
else
    echo "错误：$APP_PATH 不存在，请检查路径。"
    exit 1
fi

# 步骤 2：列出并终止 ASM 相关进程
echo "正在查找 $APP_NAME 相关进程..."
ps aux | grep -i "$APP_NAME" | grep -v "grep" | grep -v "$0"
if [ $? -eq 0 ]; then
    echo "找到以下 $APP_NAME 进程："
    PIDS=$(ps aux | grep -i "$APP_NAME" | grep -v "grep" | grep -v "$0" | awk '{print $2}')
    echo "PID 列表：$PIDS"
    echo "正在终止所有 $APP_NAME 进程..."
    for PID in $PIDS; do
        kill -9 "$PID" 2>/dev/null
        if [ $? -eq 0 ]; then
            echo "已终止进程 PID: $PID"
        else
            echo "警告：无法终止进程 PID: $PID，可能已退出或权限不足。"
        fi
    done
else
    echo "未找到 $APP_NAME 相关进程。"
fi

# 步骤 3：列出并终止 com.infogo.ServiceHelper 相关进程
echo "正在查找 $SERVICE_HELPER 相关进程..."
ps aux | grep -i "$SERVICE_HELPER" | grep -v "grep" | grep -v "$0"
if [ $? -eq 0 ]; then
    echo "找到以下 $SERVICE_HELPER 进程："
    PIDS=$(ps aux | grep -i "$SERVICE_HELPER" | grep -v "grep" | grep -v "$0" | awk '{print $2}')
    echo "PID 列表：$PIDS"
    echo "正在终止所有 $SERVICE_HELPER 进程..."
    for PID in $PIDS; do
        kill -9 "$PID" 2>/dev/null
        if [ $? -eq 0 ]; then
            echo "已终止进程 PID: $PID"
        else
            echo "警告：无法终止进程 PID: $PID，可能已退出或权限不足。"
        fi
    done
else
    echo "未找到 $SERVICE_HELPER 相关进程。"
fi

echo "操作完成！$APP_NAME 和 $SERVICE_HELPER 进程已终止，ASM 无法再次启动。"
echo "如需恢复 ASM 的可执行权限，请运行："
echo "  ./block_asm.sh restore"
```


## 卸载

如果只是临时安装一下使用后续不再使用，直接卸载就行
 1. 先关闭自启动
![](https://gcore.jsdelivr.net/gh/whcxxb/blog-imgs@main/imgs/20250427161802.png)

 2. 应用程序找到 ASM 拖进废纸篓中即可
