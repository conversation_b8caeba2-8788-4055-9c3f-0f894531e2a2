---
title: '海康rtsp流转flv格式web播放'
summary: '海康rtsp流转flv格式web播'
publishedAt: '2024-08-13'
published: false
---

## 场景

前端主流库`video.js`等无法直接播放海康的 rtsp 流需要转成`flv`格式

## 准备

1.一台服务器 2.安装 FFmpeg
3.node 环境

服务器无法访问外网只能离线安装参考文章
[FFmpeg 离线安装](https://www.cnblogs.com/badaoliumangqizhi/p/17875401.html)

## 搭建 node 转码服务

先验证 FFmpeg 是否正常安装

```sh
ffmpeg -version
```

![](https://gcore.jsdelivr.net/gh/whcxxb/blog-imgs@main/imgs/20240815135523.png)

## 初始化项目

```sh
npm init
```

## 安装转码插件

```sh
npm install node-media-server
```

## 安装 pm2

```sh
npm install -g pm2
```

## 创建 app.js

```js
const NodeMediaServer = require('node-media-server')

const config = {
  rtmp: {
    port: 1936, // RTMP 端口
    chunk_size: 60000,
    gop_cache: true,
    ping: 60,
    ping_timeout: 30
  },
  http: {
    port: 808, // HTTP 端口
    allow_origin: '*'
  }
}

var nms = new NodeMediaServer(config)
nms.run()
```

启动服务

```sh
pm2 start app.js
```

## 创建一个转流脚本

创建一个 Bash 脚本，将多个 RTSP 流同时转码为 RTMP

```sh
# 第一个 RTSP 流
ffmpeg -re -rtsp_transport tcp -i rtsp://admin:xx.xx.25.205:xx/Streaming/Channels/101 -codec h264 -b:v 1024k -an -f flv -s 1280x720 -q 10 rtmp://127.0.0.1:1936/live/stream1 &

# 第二个 RTSP 流
ffmpeg -re -rtsp_transport tcp -i rtsp://admin:xx.xx.117.xx:x/Streaming/Channels/101 -codec h264 -b:v 1024k -an -f flv -s 1280x720 -q 10 rtmp://127.0.0.1:1936/live/stream2 &
```

保存脚本为 start_streams.sh 并赋予执行权限

```sh
chmod +x start_streams.sh
```

运行脚本

```sh
./start_streams.sh
```

## 播放 FLV 视频流

在前端通过 flv.js 播放多个 FLV 流

## 安装 flv.js

```sh
npm install flv.js
```

## 引入播放

```html
<video id="videoElement" controls width="290" height="280" autoplay muted></video>
```

```js
import flvjs from 'flv.js'
const videoElement = document.getElementById('videoElement')
const flvPlayer1 = flvjs.createPlayer({
  type: 'flv',
  url: 'http://127.0.0.1:808/live/stream1.flv' // 实际替换服务器 IP 地址
})
flvPlayer1.attachMediaElement(videoElement)
flvPlayer1.load()
flvPlayer1.play()
```

播放还是挺流畅的

![](https://gcore.jsdelivr.net/gh/whcxxb/blog-imgs@main/imgs/202408151603907.png)
