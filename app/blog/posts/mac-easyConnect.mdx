---
title: 'Mac彻底卸载深信服EasyConnect(并使用 docker 隔离)'
summary: 'Mac彻底卸载深信服EasyConnect(并使用 docker 隔离)'
publishedAt: '2024-04-07'
published: true
---

公司内网使用深信服的 EasyConnect 来办公，这货会安装系统级的证书和常驻后台的服务进程。

## 删除自启动进程

打开终端运行下面的命令删除自启动项：

```bash
sudo rm /Library/LaunchDaemons/com.sangfor.EasyMonitor.plist
sudo rm /Library/LaunchAgents/com.sangfor.ECAgentProxy.plist
```

在活动监视器中检查这两个进程是否还存在：

![活动监视器检查进程](https://gcore.jsdelivr.net/gh/whcxxb/blog-imgs@main/imgs/%E6%88%AA%E5%B1%8F2024-01-16%2015.03.06.png)

把 EasyConnect App 拖进 [AppCleaner](https://freemacsoft.net/appcleaner/) 卸载，清除残留

## 删除系统证书

- 使用聚焦搜索打开`钥匙串访问`
- 在系统-证书下搜索 `Sangfor Technologies Inc`
- 右键删除找到的证书

重启电脑，到这里系统就干净了 😌

## 使用 Docker 运行 EasyConnect

[docker-easyconnect](https://github.com/docker-easyconnect/docker-easyconnect)，我们可以在容器中运行 EasyConnect

### 1. 检查环境

确保系统已安装：

- [Docker](https://formulae.brew.sh/formula/docker)
- 任意代理软件 (如 ClashX)

![检查Docker环境](https://gcore.jsdelivr.net/gh/whcxxb/blog-imgs@main/imgs/20240819085122.png)

### 2. 运行容器

如果不需要证书,使用以下命令(arm64/mips64el 架构需要加入 `-e DISABLE_PKG_VERSION_XML=1`)

```bash
docker run --device /dev/net/tun --cap-add NET_ADMIN -ti \
  -e PASSWORD=123 -e URLWIN=1 -e DISABLE_PKG_VERSION_XML=1 \
  -p 127.0.0.1:5901:5901 -p 127.0.0.1:1080:1080 -p 127.0.0.1:8888:8888 \
  hagb/docker-easyconnect:7.6.3
```

如果需要证书，需要将本地证书文件先挂载到容器中

```bash
docker run --device /dev/net/tun --cap-add NET_ADMIN -ti \
  -e PASSWORD=123 -e URLWIN=1 -e DISABLE_PKG_VERSION_XML=1 \
  -v $HOME/.ecdata:/root \
  -v /Users/<USER>/Desktop/work/other/xxx-.pfx:/usr/share/sangfor/EasyConnect/resources/user_cert/xxx-.pfx \
  -p 127.0.0.1:5901:5901 -p 127.0.0.1:1080:1080 -p 127.0.0.1:8888:8888 \
  hagb/docker-easyconnect:7.6.3
```

### 3. 使用 OrbStack 管理容器

推荐使用 [OrbStack](https://orbstack.dev/) 来管理 docker 容器，它比 Docker Desktop 更轻量：

![OrbStack界面](https://gcore.jsdelivr.net/gh/whcxxb/blog-imgs@main/imgs/20240819101225.png)

### 4. 连接 EasyConnect

1. 使用 `cmd+space` 打开聚焦搜索，输入 `screen` 打开屏幕共享
2. 连接信息：
   - 地址：127.0.0.1
   - 端口：5901
   - 密码：xxxx

![屏幕共享连接](https://gcore.jsdelivr.net/gh/whcxxb/blog-imgs@main/imgs/20240819100219.png)

如果需要导入证书，证书目录为：
`/usr/share/sangfor/EasyConnect/resources/user_cert/`

![导入证书](https://gcore.jsdelivr.net/gh/whcxxb/blog-imgs@main/imgs/20240819100540.png)

### 5. 配置代理

编辑 ClashX 配置文件增加刚才的代理配置，可以是其他代理软件比如`mihomo-party`进行配置覆写

```yaml
mode: rule
log-level: info

proxies:
  - { 'name': 'ec', 'type': 'socks5', 'server': '127.0.0.1', 'port': '1080' }
proxy-groups:

rules:
  - DOMAIN-SUFFIX,xx.xx.com,ec
```

## 问题和使用

### 检查容器网络连接

测试容器是否能连接内网：

```bash
docker exec 容器id/name busybox ping 10.11.11.xx
```

### Docker 镜像下载慢

可以通过以下方式加速：

1. 替换 Docker 镜像源
2. 使用代理：

```bash
export https_proxy=http://127.0.0.1:7890 http_proxy=http://127.0.0.1:7890 all_proxy=socks5://127.0.0.1:7890
```

### 浏览器访问内网

使用代理插件 [Proxy SwitchyOmega 3](https://chromewebstore.google.com/detail/proxy-switchyomega-3-zero/pfnededegaaopdmhkdmcofjmoldfiped) 插件来管理代理规则：

![配置SwitchyOmega](https://gcore.jsdelivr.net/gh/whcxxb/blog-imgs@main/imgs/20240819104145.png)

---

参考文章：

[Mac 删除深信服 EasyConnect 的 EasyMoniter、ECAgent 的开机启动和根证书的方法 \- V2EX](https://v2ex.com/t/762221)
