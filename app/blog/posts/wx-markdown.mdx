---
title: 微信小程序渲染Markdown实现深度思考样式
summary: 微信小程序渲染Markdown实现深度思考样式
publishedAt: 2025-03-05
published: false
---

接到一个小程序活,要求仿照 deepseek R1 的思考样式

![](https://gcore.jsdelivr.net/gh/whcxxb/blog-imgs@main/imgs/20250305173132.png)

## 构建 Towxml3.0

- 克隆项目到本地

```bash
git clone https://github.com/sbfkcel/towxml.git
```

- 安装依赖

```bash
npm install
```

- 修改配置文件

```js
// config.js

// 根据需要配置  echarts如果用不到就注释否则打包的体积很大
components: [
  'audio-player', // 音频组件，建议保留，由于小程序原生audio存在诸多问题，towxml解决了原生音频播放器的相关问题
  // 'echarts',                  // echarts图表支持
  'latex', // 数学公式支持
  'table', // 表格支持
  'todogroup', // todo支持
  'yuml', // yuml图表支持
  'img' // 图片解析组件
]
```

- 运行 `npm run build`

将 `dist` 修改为 towxml 放到 uni-app 根目录 wxcomponents 文件夹中

结构如下 并修改路径 `decode.json` 中的路径

![](https://gcore.jsdelivr.net/gh/whcxxb/blog-imgs@main/imgs/20250306155843.png)

```json
{
  "component": true,
  "usingComponents": {
    "decode": "./decode",
    "audio-player": "./audio-player/audio-player",
    "echarts": "./echarts/echarts",
    "latex": "./latex/latex",
    "table": "./table/table",
    "todogroup": "./todogroup/todogroup",
    "yuml": "./yuml/yuml",
    "img": "./img/img",
    "select-text": "./select-text"
  }
}
```

## 在页面中使用

- pages.json 配置

```json
#globalStyle

  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "uni-app",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8",
    "usingComponents": {
      "towxml": "/wxcomponents/towxml/towxml",
      "decode": "/wxcomponents/towxml/decode"
    }
  },

#pages

{
 "path": "pages/chat/index",
      "style": {
        "navigationBarTitleText": "AI聊天",
        "navigationStyle": "custom",
        "usingComponents": {
          "towxml": "/wxcomponents/towxml/towxml"
        }
}


```

- main.js 配置

```js
// #ifdef VUE3
import { createSSRApp } from 'vue'

export function createApp() {
  const app = createSSRApp(App)

  // 使用插件
  app.use(uviewPlus)

  // 添加全局属性
  app.config.globalProperties.$ws = wsClient
  app.config.globalProperties.$towxml = require('./wxcomponents/towxml/index')

  return {
    app
  }
}
// #endif'
```

- 在需要使用的页面引入,并设置不同的 class

```vue
<towxml
  v-if="item.reasonContent"
  v-show="!reasonVisible[index]"
  :nodes="getReasonHtml(item.reasonContent, index)"
></towxml>
<towxml :nodes="getHtml(item.content)"></towxml>

<script setup>
import {   getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance()

// 普通markdown
const getHtml = (markdown) => {
  return proxy.$towxml(markdown, 'markdown', {
    theme: 'light'
  })
}

// 深度思考markdown
const getReasonHtml = (markdown, index) => {
  return proxy.$towxml(markdown, 'markdown', {
    theme: `reason reason-${index}`
  })
}

```

- 在 main.wxss 中设置思考样式

```css
.h2w-reason {
  font-size: 24rpx;
  color: #94a3b8;
  line-height: 1.4;
  border-left: 0.5rpx solid #cbd5e1;
}
.h2w-reason .h2w__p {
  margin: 10rpx 0 10rpx 0 !important;
}
.h2w-reason .h2w__main {
  margin: 0 10rpx 15rpx 20rpx !important;
}
```
