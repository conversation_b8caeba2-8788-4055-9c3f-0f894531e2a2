import { notFound } from 'next/navigation'
import { CustomMDX } from 'app/components/mdx'
import { formatDate, getBlogPosts, getMdxFileMetadata } from 'app/blog/utils'
import { baseUrl } from 'app/sitemap'
import Giscus from 'app/components/Giscus'
import { execSync } from 'child_process'
import { existsSync } from 'fs'
import path from 'path'
import { Toc } from 'app/components/Toc'

export async function generateStaticParams() {
  let posts = getBlogPosts()

  return posts.map((post) => ({
    slug: post.slug
  }))
}

export async function generateMetadata(props) {
  const params = await props.params;
  let post = getBlogPosts().find((post) => post.slug === params.slug)
  if (!post) {
    return
  }

  let { title, publishedAt: publishedTime, summary: description, image } = post.metadata
  let ogImage = image ? image : `${baseUrl}/og?title=${encodeURIComponent(title)}`

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'article',
      publishedTime,
      url: `${baseUrl}/blog/${post.slug}`,
      images: [
        {
          url: ogImage
        }
      ]
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [ogImage]
    }
  }
}

export default async function Blog(props) {
  const params = await props.params;
  let post = getBlogPosts().find((post) => post.slug === params.slug)
  if (!post) {
    notFound()
  }

  const metadata = await getMdxFileMetadata(params.slug)

  // 尝试获取 Git 最后提交时间
  try {
    const filePath = path.join(process.cwd(), 'app/blog/posts', `${params.slug}.mdx`)
    if (existsSync(filePath)) {
      const date = execSync(
        `git log -1 --format=%cd --date=format:'%Y-%m-%dT%H:%M:%S%z' ${filePath}`
      )
        .toString()
        .trim()
      metadata.updatedAt = date.replace(/'/g, '').replace(/(\d{2})(\d{2})$/, '$1:$2')
    }
  } catch (error) {
    console.error('Failed to get git history:', error)
    // 如果获取 git 时间失败，使用发布时间作为更新时间
    metadata.updatedAt = post.metadata.publishedAt
  }

  return (
    <div className="relative">
      <section>
        <script
          type="application/ld+json"
          suppressHydrationWarning
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'BlogPosting',
              headline: post.metadata.title,
              datePublished: post.metadata.publishedAt,
              dateModified: metadata.updatedAt, // 使用最新的更新时间
              description: post.metadata.summary,
              image: post.metadata.image
                ? `${baseUrl}${post.metadata.image}`
                : `/og?title=${encodeURIComponent(post.metadata.title)}`,
              url: `${baseUrl}/blog/${post.slug}`,
              author: {
                '@type': 'Person',
                name: 'Reimux'
              }
            })
          }}
        />
        <h1 className="text-mz font-semibold text-3xl tracking-tighter">{post.metadata.title}</h1>
        <div className="flex justify-between items-center mt-2 mb-8 text-sm">
          <p className="text-base text-neutral-600 dark:text-neutral-400">
            {formatDate(post.metadata.publishedAt, true)}
          </p>
        </div>
        
        {/* 内嵌的可折叠目录 */}
        {/* <Toc isInline={true} /> */}

        <article className="prose lg:prose-lg max-w-none dark:prose-invert mb-10">
          <CustomMDX source={post.content} />
        </article>
        <Giscus />
      </section>

      {/* 侧边栏目录 */}
      <Toc />
    </div>
  )
}
