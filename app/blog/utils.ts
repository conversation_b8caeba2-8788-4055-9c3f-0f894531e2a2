import fs from 'fs'
import path from 'path'
import { execSync } from 'child_process'

type Metadata = {
  title: string
  publishedAt: string
  summary: string
  image?: string
  updatedAt: string
  published: boolean
}

function parseFrontmatter(fileContent: string) {
  let frontmatterRegex = /---\s*([\s\S]*?)\s*---/
  let match = frontmatterRegex.exec(fileContent)
  let frontMatterBlock = match![1]
  let content = fileContent.replace(frontmatterRegex, '').trim()
  let frontMatterLines = frontMatterBlock.trim().split('\n')
  let metadata: Partial<Metadata> = {}

  frontMatterLines.forEach((line) => {
    let [key, ...valueArr] = line.split(': ')
    let value = valueArr.join(': ').trim()
    value = value.replace(/^['"](.*)['"]$/, '$1') // Remove quotes
    
    // 根据键名处理不同类型的值
    const trimmedKey = key.trim() as keyof Metadata
    if (trimmedKey === 'published') {
      metadata[trimmedKey] = value.toLowerCase() === 'true'
    } else {
      metadata[trimmedKey] = value as any
    }
  })

  return { metadata: metadata as Metadata, content }
}

function getMDXFiles(dir) {
  return fs.readdirSync(dir).filter((file) => path.extname(file) === '.mdx')
}

function readMDXFile(filePath) {
  let rawContent = fs.readFileSync(filePath, 'utf-8')
  return parseFrontmatter(rawContent)
}

function getMDXData(dir) {
  let mdxFiles = getMDXFiles(dir)
  return mdxFiles.map((file) => {
    let { metadata, content } = readMDXFile(path.join(dir, file))
    let slug = path.basename(file, path.extname(file))

    return {
      metadata,
      slug,
      content
    }
  })
}

export function getBlogPosts() {
  const posts = getMDXData(path.join(process.cwd(), 'app', 'blog', 'posts'))
  return posts.filter(post => post.metadata.published !== false)
}

export function formatDate(date: string, includeRelative = false) {
  let currentDate = new Date()
  if (!date.includes('T')) {
    date = `${date}T00:00:00`
  }
  let targetDate = new Date(date)

  let yearsAgo = currentDate.getFullYear() - targetDate.getFullYear()
  let monthsAgo = currentDate.getMonth() - targetDate.getMonth()
  let daysAgo = currentDate.getDate() - targetDate.getDate()

  let formattedDate = ''

  if (yearsAgo > 0) {
    formattedDate = `${yearsAgo}y ago`
  } else if (monthsAgo > 0) {
    formattedDate = `${monthsAgo}mo ago`
  } else if (daysAgo > 0) {
    formattedDate = `${daysAgo}d ago`
  } else {
    formattedDate = 'Today'
  }

  let fullDate = targetDate.toLocaleString('en-US', {
    month: 'long',
    // day: '2-digit',
    day: 'numeric',
    year: 'numeric',
  })
  // let fullDate = targetDate.toLocaleString('zh-CN', {
  //   month: 'numeric',
  //   day: 'numeric',
  //   year: 'numeric',
  // })

  if (!includeRelative) {
    return fullDate
  }

  return `${fullDate} (${formattedDate})`
}

export async function getMdxFileMetadata(slug: string) {
  const filePath = path.join(process.cwd(), 'app', 'blog', 'posts', `${slug}.mdx`)
  
  // 获取文件最后一次 git 提交时间
  let updatedAt = ''
  try {
    updatedAt = execSync(
      `git log -1 --format=%cd --date=iso-strict ${filePath}`
    ).toString().trim()
    
    // 如果没有 git 记录，使用当前时间
    if (!updatedAt) {
      updatedAt = new Date().toISOString()
    }
  } catch (error) {
    // 如果出错（比如不是 git 仓库），使用当前时间
    updatedAt = new Date().toISOString()
  }

  // 返回包含更新时间的元数据
  return {
    updatedAt,
    // ... 其他元数据
  }
}
