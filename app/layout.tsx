import './global.css'
import type { Metadata } from 'next'
import { Navbar } from './components/nav'
import { Analytics } from '@vercel/analytics/react'
import { SpeedInsights } from '@vercel/speed-insights/next'
import Footer from './components/footer'
import { baseUrl } from './sitemap'
import { ThemeProvider } from 'next-themes'
import { lxgwWenKai, inter } from './lib/fonts'

export const metadata: Metadata = {
  metadataBase: new URL(baseUrl),
  title: {
    default: 'Reimux',
    template: '%s | Reimux'
  },
  description: '练习时长三年半的前端开发',
  openGraph: {
    title: 'Reimux',
    description: '练习时长三年半的前端开发',
    url: baseUrl,
    siteName: 'Reimux',
    locale: 'zh-CN',
    type: 'website'
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1
    }
  }
}

// const cx = (...classes) => classes.filter(Boolean).join(' ')

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html
      lang="zh-CN"
      suppressHydrationWarning
      className={`${lxgwWenKai.variable} ${inter.variable}`}
    >
      <body className={lxgwWenKai.className}>
        <ThemeProvider attribute="class">
          <main
            className="
          max-w-3xl mx-auto my-4 px-4
          flex flex-auto flex-col break-words"
          >
            <Navbar />
            {children}
            <Footer />
            <Analytics />
            <SpeedInsights />
          </main>
        </ThemeProvider>
      </body>
    </html>
  )
}
