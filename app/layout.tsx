import './global.css'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { Navbar } from './components/nav'
import { Analytics } from '@vercel/analytics/react'
import { SpeedInsights } from '@vercel/speed-insights/next'
import Footer from './components/footer'
import { baseUrl } from './sitemap'
import { ThemeProvider } from 'next-themes'

// 配置 Inter 字体
// const inter = Inter({
//   subsets: ['latin'],
//   display: 'swap',
//   preload: true,
//   weight: ['400', '500', '600', '700'],
//   variable: '--font-inter',
//   adjustFontFallback: false
// })

export const metadata: Metadata = {
  metadataBase: new URL(baseUrl),
  title: {
    default: 'Reimux',
    template: '%s | Reimux'
  },
  description: '练习时长三年半的前端开发',
  openGraph: {
    title: 'Reimux',
    description: '练习时长三年半的前端开发',
    url: baseUrl,
    siteName: 'Reimux',
    locale: 'zh-CN',
    type: 'website'
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1
    }
  }
}

// const cx = (...classes) => classes.filter(Boolean).join(' ')

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    // <html lang="zh-CN" className={cx(inter.variable)}>
    <html lang="zh-CN" suppressHydrationWarning>
      <head>
        <link
          rel="stylesheet"
          href="https://cdn.jsdelivr.net/npm/@callmebill/lxgw-wenkai-web@latest/style.css"
        />
      </head>

      {/* antialiased font-sans */}

      <body className="font-['LXGW_WenKai']">
        <ThemeProvider attribute="class">
          <main
            className="
          max-w-3xl mx-auto my-4 px-4 
          flex flex-auto flex-col break-words"
          >
            <Navbar />
            {children}
            <Footer />
            <Analytics />
            <SpeedInsights />
          </main>
        </ThemeProvider>
      </body>
    </html>
  )
}
