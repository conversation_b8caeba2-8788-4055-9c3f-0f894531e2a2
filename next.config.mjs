/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'gcore.jsdelivr.net',
        port: '',
        pathname: '/gh/whcxxb/blog-imgs@main/imgs/**'
      },
      {
        protocol: 'https',
        hostname: 'img.paulzzh.com',
        port: '',
        pathname: '/touhou/random'
      },
      // 如果需要添加新的图片源，可以按照下面的格式添加
      // {
      //   protocol: 'https',
      //   hostname: 'example.com',
      //   port: '',
      //   pathname: '/**'
      // }
    ]
  }
}

export default nextConfig
