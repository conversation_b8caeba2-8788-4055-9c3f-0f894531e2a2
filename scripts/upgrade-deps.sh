#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查 pnpm 是否安装
check_pnpm() {
    if ! command -v pnpm &> /dev/null; then
        print_message $RED "❌ 错误: 未找到 pnpm，请先安装 pnpm"
        print_message $YELLOW "安装命令: npm install -g pnpm"
        exit 1
    fi
}

# 检查 package.json 是否存在
check_package_json() {
    if [ ! -f "package.json" ]; then
        print_message $RED "❌ 错误: 当前目录下未找到 package.json 文件"
        exit 1
    fi
}

# 显示过时的依赖
show_outdated() {
    print_message $CYAN "🔍 检查过时的依赖..."
    
    # 检查是否有过时的依赖
    if pnpm outdated > /dev/null 2>&1; then
        print_message $GREEN "✅ 所有依赖都是最新的！"
        return 1
    else
        print_message $YELLOW "📋 发现过时的依赖:"
        echo "─────────────────────────────────────────────────────────────────────────────────"
        pnpm outdated
        echo "─────────────────────────────────────────────────────────────────────────────────"
        return 0
    fi
}

# 交互式升级
interactive_upgrade() {
    print_message $CYAN "🚀 开始交互式升级..."
    pnpm update --interactive
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 依赖升级完成！"
    else
        print_message $RED "❌ 依赖升级失败"
        exit 1
    fi
}

# 升级到最新版本
latest_upgrade() {
    print_message $CYAN "🚀 升级所有依赖到最新版本..."
    pnpm update --latest
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 依赖升级完成！"
    else
        print_message $RED "❌ 依赖升级失败"
        exit 1
    fi
}

# 主函数
main() {
    print_message $MAGENTA "🔧 依赖升级脚本"
    print_message $MAGENTA "══════════════════════════════════════════════════════"
    
    # 检查环境
    check_pnpm
    check_package_json
    
    # 显示过时的依赖
    show_outdated
    has_outdated=$?
    
    if [ $has_outdated -eq 1 ]; then
        return 0
    fi
    
    # 询问用户选择
    echo
    print_message $YELLOW "请选择升级方式:"
    print_message $NC "1. 交互式升级 (推荐) - 可以选择性升级"
    print_message $NC "2. 升级到最新版本 - 自动升级所有依赖到最新版本"
    print_message $NC "3. 仅查看，不升级"
    echo
    
    read -p "请输入选择 (1/2/3): " choice
    
    case $choice in
        1)
            interactive_upgrade
            ;;
        2)
            echo
            read -p "确定要升级所有依赖到最新版本吗？(y/n): " confirm
            if [[ $confirm =~ ^[Yy]$ ]]; then
                latest_upgrade
            else
                print_message $YELLOW "❌ 已取消升级"
            fi
            ;;
        3)
            print_message $BLUE "👀 仅查看模式，未进行任何升级"
            ;;
        *)
            print_message $RED "❌ 无效选择，已退出"
            exit 1
            ;;
    esac
}

# 运行主函数
main
