#!/usr/bin/env node

/**
 * LXGW WenKai 字体分包脚本
 * 使用 cn-font-split 将字体分割成多个小包
 * 如果原生版本失败，使用 WASM 版本
 */

const fs = require('fs');
const path = require('path');

// 尝试加载不同版本的 cn-font-split
let fontSplit;
try {
  // 首先尝试原生版本
  fontSplit = require('cn-font-split').fontSplit;
  console.log('使用原生版本 cn-font-split');
} catch (error) {
  console.log('原生版本失败，尝试 WASM 版本...');
  try {
    // 尝试 WASM 版本
    fontSplit = require('cn-font-split/wasm').fontSplit;
    console.log('使用 WASM 版本 cn-font-split');
  } catch (wasmError) {
    console.error('无法加载 cn-font-split，将使用简化的字体优化方案');
    fontSplit = null;
  }
}

async function splitFont() {
  console.log('开始分包 LXGW WenKai 字体...');

  const fontPath = './public/fonts/LXGWWenKai-Regular.ttf';
  const outputDir = './public/fonts/split';

  // 确保输出目录存在
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  // 检查字体文件是否存在
  if (!fs.existsSync(fontPath)) {
    console.error(`字体文件不存在: ${fontPath}`);
    console.log('请先运行: npm run font:download');
    process.exit(1);
  }

  if (!fontSplit) {
    console.log('cn-font-split 不可用，使用简化的字体优化方案...');
    await createSimplifiedFontConfig();
    return;
  }

  try {
    const result = await fontSplit({
      FontPath: fontPath,
      destFold: outputDir,
      targetType: 'woff2',
      chunkSize: 70, // 每个包大约70个字符
      testHTML: true, // 生成测试HTML
      reporter: true, // 显示进度
      threads: 4, // 使用4个线程
      // 预设常用字符包
      subsets: [
        {
          name: 'basic',
          text: '的一是在不了有和人这中大为上个国我以要他时来用们生到作地于出就分对成会可主发年动同工也能下过子说产种面而方后多定行学法所民得经十三之进着等部度家电力里如水化高自二理起小物现实加量都两体制机当使点从业本去把性好应开它合还因由其些然前外天政四日那社义事平形相全表间样与关各重新线内数正心反你明看原又么利比或但质气第向道命此变条只没结解问意建月公无系军很情者最立代想已通并提直题党程展五果料象员革位入常文总次品式活设及管特件长求老头基资边流路级少图山统接知较将组见计别她手角期根论运农指几九区强放决西被干做必战先回则任取据处队南给色光门即保治北造百规热领七海口东导器压志世金增争济阶油思术极交受联什认六共权收证改清己美再采转更单风切打白教速花带安场身车例真务具万每目至达走积示议声报斗完类八离华名确才科张信马节话米整空元况今集温传土许步群广石记需段研界拉林律叫且究观越织装影算低持音众书布复容儿须际商非验连断深难近矿千周委素技备半办青省列习响约支般史感劳便团往酸历市克何除消构府称太准精值号率族维划选标写存候毛亲快效斯院查江型眼王按格养易置派层片始却专状育厂京识适属圆包火住调满县局照参红细引听该铁价严龙飞'
        },
        {
          name: 'extended',
          text: '首钢铁路运输公司北京分公司技术开发部软件工程师张三李四王五赵六孙七周八吴九郑十陈百刘千杨万朱亿'
        }
      ]
    });

    console.log('字体分包完成！');
    console.log('分包结果:', result);

    // 生成字体加载配置文件
    generateFontConfig(result);

  } catch (error) {
    console.error('字体分包失败:', error);
    console.log('降级到简化的字体优化方案...');
    await createSimplifiedFontConfig();
  }
}

// 创建简化的字体配置（当 cn-font-split 不可用时）
async function createSimplifiedFontConfig() {
  console.log('创建简化的字体优化配置...');

  const outputDir = './public/fonts/split';
  const fontPath = './public/fonts/LXGWWenKai-Regular.ttf';

  // 确保输出目录存在
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  // 复制原字体文件到输出目录
  if (fs.existsSync(fontPath)) {
    const outputFontPath = path.join(outputDir, 'LXGWWenKai-Regular.ttf');
    fs.copyFileSync(fontPath, outputFontPath);
    console.log('字体文件已复制到输出目录');
  }

  // 生成简化的配置文件
  const config = {
    fontFamily: 'LXGW WenKai',
    basePath: '/fonts/split',
    mode: 'simplified', // 标记为简化模式
    fontFile: 'LXGWWenKai-Regular.ttf',
    fallback: [
      'PingFang SC',
      'Hiragino Sans GB',
      'Microsoft YaHei',
      'WenQuanYi Micro Hei',
      'sans-serif'
    ]
  };

  const configPath = './app/lib/font-config.json';
  fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
  console.log(`简化字体配置文件已生成: ${configPath}`);
  console.log('注意：使用简化模式，字体将作为单个文件加载');
}

function generateFontConfig(result) {
  const config = {
    fontFamily: 'LXGW WenKai',
    basePath: '/fonts/split',
    mode: 'chunked', // 标记为分包模式
    chunks: result.chunks || [],
    fallback: [
      'PingFang SC',
      'Hiragino Sans GB',
      'Microsoft YaHei',
      'WenQuanYi Micro Hei',
      'sans-serif'
    ]
  };

  const configPath = './app/lib/font-config.json';
  fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
  console.log(`字体配置文件已生成: ${configPath}`);
}

if (require.main === module) {
  splitFont();
}

module.exports = { splitFont };
