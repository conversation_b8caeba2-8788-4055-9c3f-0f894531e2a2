#!/usr/bin/env node

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkPnpmInstalled() {
  try {
    execSync('pnpm --version', { stdio: 'ignore' });
    return true;
  } catch (error) {
    return false;
  }
}

function checkPackageJsonExists() {
  return fs.existsSync(path.join(process.cwd(), 'package.json'));
}

async function getOutdatedPackages() {
  return new Promise((resolve, reject) => {
    log('🔍 检查过时的依赖...', 'cyan');
    
    const child = spawn('pnpm', ['outdated', '--format', 'json'], {
      stdio: ['inherit', 'pipe', 'pipe']
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      if (code === 0 || code === 1) { // pnpm outdated 返回 1 表示有过时的包
        try {
          if (stdout.trim()) {
            const outdated = JSON.parse(stdout);
            resolve(outdated);
          } else {
            resolve({});
          }
        } catch (error) {
          log('📦 所有依赖都是最新的！', 'green');
          resolve({});
        }
      } else {
        reject(new Error(`pnpm outdated 失败: ${stderr}`));
      }
    });
  });
}

function displayOutdatedPackages(outdated) {
  if (Object.keys(outdated).length === 0) {
    log('✅ 所有依赖都是最新的！', 'green');
    return false;
  }

  log('\n📋 发现过时的依赖:', 'yellow');
  log('─'.repeat(80), 'white');
  log(`${'包名'.padEnd(30)} ${'当前版本'.padEnd(15)} ${'最新版本'.padEnd(15)} ${'类型'}`, 'white');
  log('─'.repeat(80), 'white');

  Object.entries(outdated).forEach(([packageName, info]) => {
    const current = info.current || 'N/A';
    const latest = info.latest || 'N/A';
    const type = info.dependencyType || 'unknown';
    
    log(`${packageName.padEnd(30)} ${current.padEnd(15)} ${latest.padEnd(15)} ${type}`, 'white');
  });

  log('─'.repeat(80), 'white');
  return true;
}

function promptUser(question) {
  return new Promise((resolve) => {
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    rl.question(question, (answer) => {
      rl.close();
      resolve(answer.toLowerCase().trim());
    });
  });
}

async function upgradePackages(mode = 'interactive') {
  return new Promise((resolve, reject) => {
    log(`\n🚀 开始升级依赖 (${mode === 'latest' ? '升级到最新版本' : '交互式升级'})...`, 'cyan');
    
    const args = mode === 'latest' 
      ? ['update', '--latest'] 
      : ['update', '--interactive'];

    const child = spawn('pnpm', args, {
      stdio: 'inherit'
    });

    child.on('close', (code) => {
      if (code === 0) {
        log('✅ 依赖升级完成！', 'green');
        resolve();
      } else {
        reject(new Error(`依赖升级失败，退出码: ${code}`));
      }
    });
  });
}

async function main() {
  try {
    log('🔧 依赖升级脚本', 'magenta');
    log('═'.repeat(50), 'magenta');

    // 检查 pnpm 是否安装
    if (!checkPnpmInstalled()) {
      log('❌ 错误: 未找到 pnpm，请先安装 pnpm', 'red');
      log('安装命令: npm install -g pnpm', 'yellow');
      process.exit(1);
    }

    // 检查 package.json 是否存在
    if (!checkPackageJsonExists()) {
      log('❌ 错误: 当前目录下未找到 package.json 文件', 'red');
      process.exit(1);
    }

    // 获取过时的包
    const outdated = await getOutdatedPackages();
    
    // 显示过时的包
    const hasOutdated = displayOutdatedPackages(outdated);
    
    if (!hasOutdated) {
      return;
    }

    // 询问用户升级方式
    log('\n请选择升级方式:', 'yellow');
    log('1. 交互式升级 (推荐) - 可以选择性升级', 'white');
    log('2. 升级到最新版本 - 自动升级所有依赖到最新版本', 'white');
    log('3. 仅查看，不升级', 'white');

    const choice = await promptUser('请输入选择 (1/2/3): ');

    switch (choice) {
      case '1':
        await upgradePackages('interactive');
        break;
      case '2':
        const confirm = await promptUser('确定要升级所有依赖到最新版本吗？(y/n): ');
        if (confirm === 'y' || confirm === 'yes') {
          await upgradePackages('latest');
        } else {
          log('❌ 已取消升级', 'yellow');
        }
        break;
      case '3':
        log('👀 仅查看模式，未进行任何升级', 'blue');
        break;
      default:
        log('❌ 无效选择，已退出', 'red');
        break;
    }

  } catch (error) {
    log(`❌ 错误: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 运行主函数
main();
