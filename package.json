{"private": true, "scripts": {"dev": "next dev --turbopack", "build": "npm run font:split && next build", "start": "next start", "font:split": "node scripts/font-split.js", "font:download": "curl -L 'https://github.com/lxgw/LxgwWenKai/releases/download/v1.520/LXGWWenKai-Regular.ttf' -o public/fonts/LXGWWenKai-Regular.ttf", "upgrade-deps": "node scripts/upgrade-deps.js"}, "dependencies": {"@types/react": "19.1.9", "@types/react-dom": "19.1.7", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "cn-font-split": "^7.6.5", "geist": "1.4.2", "lucide-react": "^0.534.0", "next": "15.4.5", "next-mdx-remote": "^5.0.0", "next-themes": "^0.4.6", "react": "19.1.1", "react-dom": "19.1.1", "react-medium-image-zoom": "^5.3.0", "shiki": "^3.8.1", "sugar-high": "^0.9.3", "typescript": "5.8.3"}, "devDependencies": {"@iconify-json/solar": "^1.2.2", "@iconify/tailwind4": "^1.0.6", "@shikijs/transformers": "^3.8.1", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/node": "24.1.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "ts-node": "^10.9.2"}, "pnpm": {"overrides": {"@types/react": "19.1.9", "@types/react-dom": "19.1.7"}}}