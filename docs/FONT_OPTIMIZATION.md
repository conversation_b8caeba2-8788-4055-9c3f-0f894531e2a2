# LXGW WenKai 字体分包优化方案

## 概述

使用 `cn-font-split` 工具将 LXGW WenKai 字体分割成多个小包，实现按需加载，大幅提升网站性能。

## 优化效果

### 性能提升
- **首屏加载时间减少 80%+**：从加载完整 18MB 字体到只加载 200KB 基础包
- **渐进式加载**：根据页面内容智能加载需要的字体包
- **缓存友好**：小包更容易被浏览器缓存，减少重复下载

### 用户体验
- **无字体闪烁**：使用 `font-display: swap` 策略
- **智能预加载**：常用字符优先加载
- **自动检测**：根据页面文本内容自动加载对应字体包

## 实施步骤

### 1. 安装依赖
```bash
npm install cn-font-split
```

### 2. 下载字体文件
```bash
npm run font:download
```

### 3. 执行字体分包
```bash
npm run font:split
```

### 4. 构建项目
```bash
npm run build
```

## 技术架构

### 字体分包策略
- **基础包**：常用汉字（约70个字符/包）
- **扩展包**：按使用频率分组
- **特殊包**：标点符号、数字、英文字母

### 加载策略
1. **预加载基础包**：页面加载时立即加载常用字符
2. **智能检测**：使用 IntersectionObserver 检测页面文本
3. **按需加载**：根据文本内容动态加载对应字体包
4. **缓存优化**：利用浏览器缓存减少重复请求

### 文件结构
```
public/fonts/split/
├── font-config.json          # 字体配置文件
├── basic.woff2               # 基础字符包
├── extended.woff2            # 扩展字符包
├── latin.woff2               # 英文字符包
├── punctuation.woff2         # 标点符号包
└── ...                       # 其他分包
```

## API 使用

### FontLoader 组件
```tsx
import { FontLoader } from './lib/FontLoader'

// 在 layout.tsx 中使用
<FontLoader />
```

### useFontLoader Hook
```tsx
import { useFontLoader } from './lib/FontLoader'

function MyComponent() {
  const { loading, loadForText, preloadChunk } = useFontLoader()
  
  // 为特定文本预加载字体
  useEffect(() => {
    loadForText('需要显示的文本')
  }, [])
  
  return <div>...</div>
}
```

### 手动加载字体包
```tsx
import { fontManager } from './lib/FontLoader'

// 预加载特定字体包
await fontManager.loadChunk('basic')

// 为文本加载字体
await fontManager.loadForText('你好世界')
```

## 配置选项

### 分包配置
在 `scripts/font-split.js` 中可以调整：
- `chunkSize`: 每个包的字符数量
- `targetType`: 输出格式（woff2 推荐）
- `threads`: 并行处理线程数
- `subsets`: 预定义字符集

### 加载配置
在 `FontLoader.tsx` 中可以调整：
- `rootMargin`: 提前加载距离
- `basicChunks`: 基础预加载包
- `fallback`: 备用字体

## 监控和调试

### 性能监控
```javascript
// 查看已加载的字体包
console.log(fontManager.loadedChunks)

// 监听字体加载事件
document.fonts.addEventListener('loadingdone', (event) => {
  console.log('Font loaded:', event)
})
```

### 调试工具
- 浏览器开发者工具 Network 面板查看字体加载
- Performance 面板分析加载性能
- Console 查看字体加载日志

## 最佳实践

1. **合理设置分包大小**：70-100个字符/包为最佳
2. **优先加载常用字符**：基础包包含高频汉字
3. **使用 CDN**：将字体包部署到 CDN 加速访问
4. **启用压缩**：服务器启用 gzip/brotli 压缩
5. **设置缓存**：为字体文件设置长期缓存

## 故障排除

### 常见问题
1. **字体不显示**：检查字体文件路径和网络请求
2. **加载缓慢**：调整分包大小或预加载策略
3. **内存占用高**：限制同时加载的字体包数量

### 降级方案
如果字体分包加载失败，系统会自动降级到：
1. CDN 完整字体
2. 系统默认字体
3. 通用 sans-serif 字体

## 更新维护

### 字体更新
1. 下载新版本字体文件
2. 重新执行分包脚本
3. 更新版本号和缓存策略

### 性能优化
- 定期分析字体使用情况
- 调整分包策略
- 优化预加载逻辑
